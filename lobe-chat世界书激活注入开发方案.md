# Lobe-Chat 世界书激活注入开发方案

## 📋 项目背景

### 现状分析
lobe-chat已经具备完整的世界书基础设施：
- ✅ **数据结构完整**：worldbook_chunks表已包含所有47个字段，覆盖SillyTavern的全部功能
- ✅ **存储系统完善**：通过chunks表存储内容，支持向量化
- ❌ **缺少激活逻辑**：没有实现世界书的自动激活和注入机制

### 核心问题
现有的semanticSearchForChat只支持文件搜索，不支持世界书条目搜索：
```typescript
// 当前实现只搜索文件
.leftJoin(fileChunks, eq(chunks.id, fileChunks.chunkId))
.where(inArray(fileChunks.fileId, fileIds))
```

需要扩展支持世界书条目的语义搜索。

## 🔄 技术实现链路图

### 完整处理流程
```mermaid
graph TD
    A[用户发送消息] --> B[触发generateAIChat]
    B --> C[世界书激活检查]
    C --> D[获取会话关联的世界书]
    D --> E[并行执行三种激活模式]

    E --> F1[始终激活模式]
    E --> F2[关键词激活模式]
    E --> F3[语义激活模式]

    F1 --> G[合并激活结果]
    F2 --> G
    F3 --> G

    G --> H[应用过滤条件]
    H --> I[时间效果检查]
    I --> J[概率控制]
    J --> K[预算控制]
    K --> L[递归激活扫描]
    L --> M[按优先级排序]
    M --> N[按位置分组]
    N --> O[注入到消息上下文]
    O --> P[AI生成回答]
```

### 详细步骤说明

#### 1. 激活触发 (generateAIChat)
- 在现有RAG流程之前插入世界书激活逻辑
- 检查当前Agent是否启用世界书功能

#### 2. 获取关联世界书
- 从Agent配置获取绑定的世界书ID列表
- 查询worldbook_chunks表获取所有启用的条目

#### 3. 三种激活模式并行执行

##### 3.1 始终激活模式
```sql
SELECT * FROM worldbook_chunks
WHERE worldbook_id IN (...)
AND enabled = true
AND activation_mode = 'always'
```

##### 3.2 关键词激活模式
```typescript
// 对每个条目检查关键词匹配
for (const entry of entries) {
  const primaryMatch = entry.keys.some(key =>
    matchKeyword(userMessage, key, entry)
  );

  if (primaryMatch && checkSecondaryKeys(entry, userMessage)) {
    activatedEntries.push(entry);
  }
}
```

##### 3.3 语义激活模式
**问题**：现有semanticSearchForChat不支持世界书
**解决方案**：扩展ChunkModel添加semanticSearchForWorldbook方法

```typescript
// 新增方法
semanticSearchForWorldbook = async ({
  embedding,
  worldbookIds,
  query,
}: {
  embedding: number[];
  worldbookIds: string[];
  query: string;
}) => {
  const similarity = sql<number>`1 - (${cosineDistance(embeddings.embeddings, embedding)})`;

  return this.db
    .select({
      id: chunks.id,
      text: chunks.text,
      similarity,
      worldbookChunk: worldbookChunks,
    })
    .from(chunks)
    .leftJoin(embeddings, eq(chunks.id, embeddings.chunkId))
    .leftJoin(worldbookChunks, eq(chunks.id, worldbookChunks.chunkId))
    .where(
      and(
        inArray(worldbookChunks.worldbookId, worldbookIds),
        eq(worldbookChunks.enabled, true),
        gt(similarity, 0.7)
      )
    )
    .orderBy(desc(similarity))
    .limit(10);
};
```

#### 4. 过滤和控制检查

##### 4.1 时间效果检查
```typescript
// 检查sticky、cooldown、delay状态
const timeEffectResult = await checkTimeEffects(entry.id, sessionId);
if (!timeEffectResult.canActivate) {
  continue; // 跳过此条目
}
```

##### 4.2 概率控制
```typescript
if (entry.useProbability && entry.probability < 100) {
  const roll = Math.random() * 100;
  if (roll > entry.probability) {
    continue; // 概率检查失败
  }
}
```

##### 4.3 预算控制
```typescript
const totalTokens = activatedEntries.reduce((sum, e) => sum + e.tokens, 0);
if (totalTokens > maxTokenBudget) {
  break; // 停止激活更多条目
}
```

#### 5. 递归激活扫描
```typescript
let currentDepth = 0;
const maxDepth = 3;

while (currentDepth < maxDepth && newActivations.length > 0) {
  const recursiveContent = newActivations.map(e => e.content).join('\n');
  const recursiveActivated = await keywordActivation(recursiveContent);

  // 过滤已激活和排除递归的条目
  const filteredNew = recursiveActivated.filter(e =>
    !allActivated.has(e.id) && !e.excludeRecursion
  );

  newActivations = filteredNew;
  currentDepth++;
}
```

#### 6. 排序和分组
```typescript
// 按order字段降序排序
activatedEntries.sort((a, b) => b.order - a.order);

// 按position分组
const beforeEntries = activatedEntries.filter(e => e.position === 'before');
const afterEntries = activatedEntries.filter(e => e.position === 'after');
const depthEntries = activatedEntries.filter(e => e.position === 'at_depth');
```

#### 7. 内容注入
```typescript
// 注入到消息上下文的不同位置
if (beforeEntries.length > 0) {
  messages.unshift({
    role: 'system',
    content: buildWorldbookContent(beforeEntries)
  });
}

if (afterEntries.length > 0) {
  const insertIndex = findAgentDescriptionIndex(messages);
  messages.splice(insertIndex + 1, 0, {
    role: 'system',
    content: buildWorldbookContent(afterEntries)
  });
}
```

## 🔧 核心技术组件

### 1. WorldbookActivationEngine
**职责**：统一管理激活流程
**位置**：`src/services/worldbook/activation/WorldbookActivationEngine.ts`

### 2. KeywordMatcher
**职责**：关键词匹配逻辑
**核心功能**：
- 正则表达式支持
- 主次关键词逻辑组合
- 大小写和全词匹配控制

### 3. SemanticMatcher
**职责**：语义匹配逻辑
**核心功能**：
- 扩展ChunkModel支持世界书搜索
- 向量相似度计算
- 阈值控制

### 4. TimeEffectManager
**职责**：时间效果管理
**核心功能**：
- sticky/cooldown/delay状态跟踪
- 会话级状态管理
- 自动计数器递减

### 5. ContextInjector
**职责**：内容注入
**核心功能**：
- 多位置注入支持
- 内容格式化
- 透明集成到现有流程

## 🛠️ 具体实现方案

### 扩展ChunkModel支持世界书搜索

#### 问题分析
现有semanticSearchForChat只支持文件搜索：
```typescript
// 当前实现
.leftJoin(fileChunks, eq(chunks.id, fileChunks.chunkId))
.where(inArray(fileChunks.fileId, fileIds))
```

#### 解决方案
在ChunkModel中新增semanticSearchForWorldbook方法：

```typescript
// src/database/models/chunk.ts
semanticSearchForWorldbook = async ({
  embedding,
  worldbookIds,
  threshold = 0.7,
}: {
  embedding: number[];
  worldbookIds: string[];
  threshold?: number;
}) => {
  const similarity = sql<number>`1 - (${cosineDistance(embeddings.embeddings, embedding)})`;

  const result = await this.db
    .select({
      id: chunks.id,
      text: chunks.text,
      similarity,
      worldbookChunkId: worldbookChunks.id,
      title: worldbookChunks.title,
      order: worldbookChunks.order,
      position: worldbookChunks.position,
    })
    .from(chunks)
    .leftJoin(embeddings, eq(chunks.id, embeddings.chunkId))
    .leftJoin(worldbookChunks, eq(chunks.id, worldbookChunks.chunkId))
    .where(
      and(
        inArray(worldbookChunks.worldbookId, worldbookIds),
        eq(worldbookChunks.enabled, true),
        eq(worldbookChunks.activationMode, 'semantic'),
        gt(similarity, threshold)
      )
    )
    .orderBy(desc(similarity))
    .limit(15);

  return result;
};
```

### 集成到generateAIChat流程

#### 修改位置
在`src/store/chat/slices/aiChat/actions/generateAIChat.ts`中，RAG流程之前添加：

```typescript
// 在现有RAG检查之前添加世界书激活
if (agentSelectors.hasEnabledWorldbooks(agentStoreState)) {
  const worldbookEngine = new WorldbookActivationEngine();
  const activatedEntries = await worldbookEngine.activate({
    sessionId: activeId,
    userMessage: params.userMessage,
    messages: messages,
    agent: agentStoreState.activeAgent
  });

  if (activatedEntries.length > 0) {
    const injector = new ContextInjector();
    messages = await injector.inject(messages, activatedEntries);
  }
}

// 继续现有RAG流程
if (params?.ragQuery) {
  // ... 现有RAG代码
}
```

### 关键词匹配实现

#### KeywordMatcher类
```typescript
// src/services/worldbook/activation/KeywordMatcher.ts
export class KeywordMatcher {
  async matchEntry(
    entry: WorldbookEntry,
    content: string
  ): Promise<boolean> {
    // 1. 主关键词匹配
    const primaryMatch = entry.keys.some(key =>
      this.matchKeyword(content, key, entry)
    );

    if (!primaryMatch) return false;

    // 2. 次关键词逻辑检查
    if (entry.keysSecondary.length === 0) return true;

    return this.checkSecondaryLogic(entry, content);
  }

  private matchKeyword(
    content: string,
    keyword: string,
    entry: WorldbookEntry
  ): boolean {
    // 正则表达式匹配
    if (entry.useRegex) {
      try {
        const regex = new RegExp(keyword, entry.caseSensitive ? 'g' : 'gi');
        return regex.test(content);
      } catch {
        return false;
      }
    }

    // 普通文本匹配
    const searchContent = entry.caseSensitive ? content : content.toLowerCase();
    const searchKeyword = entry.caseSensitive ? keyword : keyword.toLowerCase();

    if (entry.matchWholeWords) {
      const wordRegex = new RegExp(`\\b${escapeRegex(searchKeyword)}\\b`, 'gi');
      return wordRegex.test(searchContent);
    }

    return searchContent.includes(searchKeyword);
  }

  private checkSecondaryLogic(
    entry: WorldbookEntry,
    content: string
  ): boolean {
    const matches = entry.keysSecondary.map(key =>
      this.matchKeyword(content, key, entry)
    );

    switch (entry.selectiveLogic) {
      case 'and_any':
        return matches.some(m => m);
      case 'and_all':
        return matches.every(m => m);
      case 'not_any':
        return !matches.some(m => m);
      case 'not_all':
        return !matches.every(m => m);
      default:
        return matches.some(m => m);
    }
  }
}
```

### 时间效果管理

#### 数据库状态表
需要新增激活状态跟踪表：
```sql
CREATE TABLE worldbook_activation_states (
  id TEXT PRIMARY KEY,
  entry_id TEXT REFERENCES worldbook_chunks(id) ON DELETE CASCADE,
  session_id TEXT REFERENCES sessions(id) ON DELETE CASCADE,
  last_activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  sticky_remaining INTEGER DEFAULT 0,
  cooldown_remaining INTEGER DEFAULT 0,
  delay_remaining INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_activation_states_entry_session
ON worldbook_activation_states(entry_id, session_id);
```

#### TimeEffectManager类
```typescript
// src/services/worldbook/activation/TimeEffectManager.ts
export class TimeEffectManager {
  async checkTimeEffects(
    entryId: string,
    sessionId: string
  ): Promise<{ canActivate: boolean; reason: string; autoActivate?: boolean }> {
    const state = await this.getActivationState(entryId, sessionId);

    if (!state) {
      return { canActivate: true, reason: 'no_previous_state' };
    }

    // 检查冷却期
    if (state.cooldownRemaining > 0) {
      return {
        canActivate: false,
        reason: 'cooldown',
        remainingTurns: state.cooldownRemaining
      };
    }

    // 检查延迟
    if (state.delayRemaining > 0) {
      return {
        canActivate: false,
        reason: 'delay',
        remainingTurns: state.delayRemaining
      };
    }

    // 检查粘性效果
    if (state.stickyRemaining > 0) {
      return {
        canActivate: true,
        reason: 'sticky',
        autoActivate: true
      };
    }

    return { canActivate: true, reason: 'normal' };
  }

  async updateActivationState(
    entryId: string,
    sessionId: string,
    entry: WorldbookEntry
  ): Promise<void> {
    await this.db.insert(worldbookActivationStates)
      .values({
        entryId,
        sessionId,
        lastActivatedAt: new Date(),
        stickyRemaining: entry.sticky || 0,
        cooldownRemaining: entry.cooldown || 0,
        delayRemaining: entry.delay || 0,
        userId: this.userId
      })
      .onConflictDoUpdate({
        target: [worldbookActivationStates.entryId, worldbookActivationStates.sessionId],
        set: {
          lastActivatedAt: new Date(),
          stickyRemaining: entry.sticky || 0,
          cooldownRemaining: entry.cooldown || 0,
          delayRemaining: entry.delay || 0,
          updatedAt: new Date()
        }
      });
  }

  async decrementCounters(sessionId: string): Promise<void> {
    // 每轮对话后递减计数器
    await this.db.update(worldbookActivationStates)
      .set({
        stickyRemaining: sql`GREATEST(sticky_remaining - 1, 0)`,
        cooldownRemaining: sql`GREATEST(cooldown_remaining - 1, 0)`,
        delayRemaining: sql`GREATEST(delay_remaining - 1, 0)`,
        updatedAt: new Date()
      })
      .where(eq(worldbookActivationStates.sessionId, sessionId));
  }
}
```

## 📅 开发计划

### 第一阶段：基础激活功能（1周）
- [ ] 扩展ChunkModel支持世界书语义搜索
- [ ] 实现WorldbookActivationEngine基础框架
- [ ] 实现始终激活和关键词激活
- [ ] 集成到generateAIChat流程

### 第二阶段：高级功能（1周）
- [ ] 实现时间效果系统
- [ ] 实现概率控制
- [ ] 实现递归激活扫描
- [ ] 实现预算控制

### 第三阶段：优化和测试（1周）
- [ ] 性能优化和缓存
- [ ] 全面测试
- [ ] 调试工具和监控
- [ ] 文档完善

## 🎯 关键技术决策

### 1. 复用现有架构
- ✅ 复用chunks表存储世界书内容
- ✅ 复用embeddings表存储向量
- ✅ 复用现有向量化流程
- ❌ 不能直接复用semanticSearchForChat（需要扩展）

### 2. 数据库设计
- ✅ worldbook_chunks表字段已完整，无需修改
- ➕ 新增worldbook_activation_states表跟踪时间效果
- ➕ 扩展ChunkModel支持世界书搜索

### 3. 集成策略
- 在RAG流程之前执行世界书激活
- 透明注入到消息上下文
- 不影响现有功能

---

*本方案基于对现有代码的深入分析，确保技术方案的准确性和可实施性。*

## 🏗️ 技术实现方案

### 在lobe-chat中的集成点

#### 现有系统利用
lobe-chat已经有完善的RAG（检索增强生成）系统，我们可以复用：
- **数据存储**：chunks表存储世界书内容，embeddings表存储向量
- **语义搜索**：现有的语义搜索功能
- **消息生成**：现有的AI对话生成流程

#### 集成位置
在用户发送消息后，AI生成回答前，插入世界书激活逻辑：
```
用户消息 → [世界书激活] → AI生成回答
```

### 核心技术组件

#### 1. 世界书激活引擎（WorldbookActivationEngine）
**职责**：统一管理所有激活逻辑
**主要功能**：
- 协调三种激活模式
- 处理时间效果
- 管理预算控制
- 处理递归激活

#### 2. 关键词匹配器（KeywordMatcher）
**职责**：处理关键词匹配激活
**技术特点**：
- 支持正则表达式
- 支持大小写控制
- 支持全词匹配
- 支持主次关键词逻辑组合

#### 3. 语义匹配器（SemanticMatcher）
**职责**：处理AI理解的语义激活
**技术特点**：
- 复用现有向量搜索
- 支持相似度阈值控制
- 支持查询重写优化

#### 4. 时间效果管理器（TimeEffectManager）
**职责**：管理sticky、cooldown、delay效果
**技术特点**：
- 会话级状态跟踪
- 自动计数器递减
- 状态持久化存储

#### 5. 内容注入器（ContentInjector）
**职责**：将激活的内容注入到对话中
**技术特点**：
- 支持多种注入位置
- 智能内容排序
- 透明的内容整合

### 数据库设计

#### 扩展现有表结构
在现有worldbook_chunks表基础上添加字段：

```sql
-- 激活控制字段
ALTER TABLE worldbook_chunks ADD COLUMN always_active BOOLEAN DEFAULT false;
ALTER TABLE worldbook_chunks ADD COLUMN activation_modes TEXT[] DEFAULT '{"keyword", "semantic"}';

-- 时间效果字段
ALTER TABLE worldbook_chunks ADD COLUMN sticky_duration INTEGER DEFAULT 0;
ALTER TABLE worldbook_chunks ADD COLUMN cooldown_duration INTEGER DEFAULT 0;
ALTER TABLE worldbook_chunks ADD COLUMN delay_duration INTEGER DEFAULT 0;

-- 概率控制字段
ALTER TABLE worldbook_chunks ADD COLUMN probability INTEGER DEFAULT 100;
ALTER TABLE worldbook_chunks ADD COLUMN use_probability BOOLEAN DEFAULT false;
```

#### 新增状态跟踪表
```sql
-- 激活状态跟踪表
CREATE TABLE worldbook_activation_states (
  id TEXT PRIMARY KEY,
  entry_id TEXT REFERENCES worldbook_chunks(id),
  session_id TEXT REFERENCES sessions(id),
  last_activated_at TIMESTAMP,
  sticky_remaining INTEGER DEFAULT 0,
  cooldown_remaining INTEGER DEFAULT 0,
  delay_remaining INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### API设计

#### 激活相关接口
```typescript
// 获取激活状态
GET /api/worldbook/activation/status/:sessionId
Response: {
  activatedEntries: ActivatedEntry[];
  totalTokens: number;
  activationSummary: {
    alwaysActive: number;
    keywordActivated: number;
    semanticActivated: number;
    recursiveActivated: number;
  };
}

// 手动测试激活
POST /api/worldbook/activation/test
Body: {
  sessionId: string;
  testMessage: string;
  dryRun: boolean;
}
```

## 📊 开发计划

### 第一阶段：基础激活功能（2周）
**目标**：实现基本的世界书激活
**任务**：
- [ ] 实现始终激活模式
- [ ] 实现基础关键词匹配
- [ ] 集成到现有消息生成流程
- [ ] 基础的内容注入功能

### 第二阶段：高级激活功能（2周）
**目标**：完善激活逻辑
**任务**：
- [ ] 实现语义激活（复用现有RAG）
- [ ] 实现主次关键词逻辑组合
- [ ] 实现概率控制
- [ ] 实现预算控制

### 第三阶段：时间效果和递归（2周）
**目标**：实现高级功能
**任务**：
- [ ] 实现时间效果系统
- [ ] 实现递归激活
- [ ] 实现激活状态管理
- [ ] 性能优化

### 第四阶段：用户界面和测试（1周）
**目标**：完善用户体验
**任务**：
- [ ] 激活状态显示界面
- [ ] 配置管理界面
- [ ] 调试和测试工具
- [ ] 全面测试和文档

## 🎨 用户界面设计

### 激活状态显示
- **实时显示**：当前激活的世界书条目
- **激活原因**：显示为什么激活（关键词匹配、语义相关等）
- **激活历史**：查看历史激活记录

### 配置界面
- **激活模式开关**：控制三种激活模式的启用
- **参数调节**：概率、时间效果、预算等参数设置
- **测试工具**：输入测试消息，查看会激活哪些条目

### 调试工具
- **激活日志**：详细的激活过程日志
- **性能监控**：激活耗时、内存使用等指标
- **问题诊断**：为什么某个条目没有激活

## 🔍 风险评估和解决方案

### 性能风险
**风险**：激活逻辑可能影响消息生成速度
**解决方案**：
- 并行处理多种激活模式
- 缓存激活结果
- 智能预算控制

### 准确性风险
**风险**：激活不准确，无关内容被激活
**解决方案**：
- 多层检查机制
- 用户反馈和调整
- 智能阈值控制

### 复杂性风险
**风险**：功能太复杂，用户难以理解和配置
**解决方案**：
- 提供默认配置
- 分层界面设计
- 详细的使用指南

## 📈 成功指标

### 功能指标
- [ ] 支持SillyTavern的所有核心功能
- [ ] 激活准确率 > 90%
- [ ] 用户满意度 > 4.5/5

### 性能指标
- [ ] 激活处理时间 < 200ms
- [ ] 内存使用增加 < 20%
- [ ] 系统稳定性不受影响

### 用户体验指标
- [ ] 配置界面易用性 > 4.0/5
- [ ] 学习成本 < 30分钟
- [ ] 功能发现率 > 80%

---

*本方案采用通俗易懂的语言，面向产品人员和开发人员，确保技术方案的可理解性和可实施性。*

## 🔍 需求分析

### SillyTavern世界书功能特性分析

#### 核心激活机制
1. **关键词匹配激活**
   - 主关键词（Primary Keys）匹配
   - 次关键词（Secondary Keys）逻辑组合
   - 支持正则表达式、大小写敏感、全词匹配

2. **始终激活模式**
   - 无条件激活的世界书条目
   - 用于核心世界观和背景信息

3. **递归扫描机制**
   - 激活的条目内容可触发其他条目激活
   - 支持多层递归深度控制
   - 防止无限递归的安全机制

#### 高级功能特性
1. **概率控制**
   - 条目激活的概率阈值
   - 随机性引入，增加对话多样性

2. **时间效果**
   - Sticky：条目激活后持续生效轮数
   - Cooldown：条目激活后的冷却期
   - Delay：条目激活前的延迟轮数

3. **位置控制**
   - Before：插入到角色描述之前
   - After：插入到角色描述之后
   - At Depth：插入到指定深度位置

4. **优先级管理**
   - Order字段控制条目优先级
   - 预算控制限制总token使用量

### Lobe-Chat现有能力分析

#### RAG系统基础设施
1. **数据层**
   - `chunks`表：存储文本块内容
   - `embeddings`表：存储向量嵌入
   - `knowledge_bases`表：知识库管理
   - 完整的向量化和检索能力

2. **服务层**
   - `ChunkModel`：文本块管理
   - `EmbeddingModel`：向量嵌入管理
   - `ragService`：语义搜索服务
   - 成熟的向量化流程

3. **世界书基础设施**
   - `worldbooks`表：世界书管理
   - `worldbook_chunks`表：世界书条目管理
   - `WorldbookModel`：世界书CRUD操作
   - `WorldbookChunkModel`：条目CRUD操作

#### 消息生成流程
1. **RAG集成点**
   - `generateAIChat`：主要消息生成入口
   - `internal_retrieveChunks`：语义检索功能
   - `knowledgeBaseQAPrompts`：上下文构建

2. **现有激活逻辑**
   - 基于知识库的语义搜索
   - 查询重写和优化
   - 上下文注入机制

## 🏗️ 系统架构设计

### 整体架构概览

```
用户消息 → 世界书激活引擎 → 内容注入 → AI生成
    ↓           ↓              ↓
  消息分析   多模式激活      上下文构建
    ↓           ↓              ↓
  关键词提取  条目匹配       Prompt组装
    ↓           ↓              ↓
  语义向量化  递归扫描       位置控制
```

### 核心组件设计

#### 1. 世界书激活引擎（WorldbookActivationEngine）
**职责**：统一管理所有激活模式，协调激活流程
**位置**：`src/services/worldbook/activation/`

主要功能：
- 激活模式路由和调度
- 激活结果聚合和去重
- 递归扫描控制
- 性能监控和优化

#### 2. 多模式激活器（Multi-Mode Activators）
**职责**：实现不同的激活策略

##### 2.1 始终激活器（AlwaysActivator）
- 处理无条件激活的条目
- 集成到消息生成的预处理阶段

##### 2.2 语义激活器（SemanticActivator）
- 复用现有`ragService.semanticSearchForChat`
- 基于向量相似度的智能匹配
- 支持查询重写和优化

##### 2.3 关键词激活器（KeywordActivator）
- 实现SillyTavern的`matchKeys`逻辑
- 支持正则表达式、大小写控制
- 主次关键词逻辑组合

#### 3. 递归扫描器（RecursionScanner）
**职责**：处理条目间的相互激活
**核心算法**：
- 深度优先扫描策略
- 循环检测和防护
- 扫描深度限制

#### 4. 上下文注入器（ContextInjector）
**职责**：将激活的条目注入到对话上下文
**集成点**：`generateAIChat`流程中的prompt构建阶段

---

*本方案基于对SillyTavern和Lobe-Chat两个项目的深入分析，旨在实现功能完整、性能优秀、用户友好的世界书激活注入系统。*
